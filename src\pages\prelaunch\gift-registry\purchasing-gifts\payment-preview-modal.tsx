import { useState, useEffect } from "react";
import {
  GuestGiftsAPI,
  PaymentFeeResponse,
} from "../../../../lib/apis/guestGiftsApi";
import { guestTokenManager } from "../../../../lib/utils/guestTokenManager";
import { toast } from "react-toastify";

interface PaymentPreviewModalProps {
  onContinue: () => void;
  onClose: () => void;
  reservationId: string;
}

export const PaymentPreviewModal = ({
  onContinue,
  onClose,
  reservationId,
}: PaymentPreviewModalProps) => {
  const [feeData, setFeeData] = useState<PaymentFeeResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPaymentFee = async () => {
      try {
        setLoading(true);
        const accessToken = await guestTokenManager.getGuestAccessToken();
        if (!accessToken) {
          throw new Error("No access token found");
        }

        const response = await GuestGiftsAPI.calculatePaymentFee(reservationId);
        setFeeData(response);
      } catch (err) {
        const errorMsg =
          typeof err === "object" && err && "message" in err
            ? (err as { message?: string }).message ?? "Failed to calculate fee"
            : "Failed to calculate fee";
        setError(errorMsg);
        toast.error(errorMsg);
      } finally {
        setLoading(false);
      }
    };

    if (reservationId) {
      fetchPaymentFee();
    }
  }, [reservationId]);

  const calculateTotal = () => {
    if (!feeData) return "0.00";
    const amount = parseFloat(feeData.amount);
    const fee = parseFloat(feeData.fee);
    return (amount + fee).toFixed(2);
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-[#00002666]/40 flex items-center justify-center z-50 font-rethink">
        <div className="bg-white rounded-3xl w-full md:w-[522px] mx-4 relative p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#4D55F2] mx-auto"></div>
            <p className="mt-4 text-gray-600">Calculating payment fee...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !feeData) {
    return (
      <div className="fixed inset-0 bg-[#00002666]/40 flex items-center justify-center z-50 font-rethink">
        <div className="bg-white rounded-3xl w-full md:w-[522px] mx-4 relative p-8">
          <div className="text-center">
            <p className="text-red-500 text-lg font-medium mb-4">
              {error || "Failed to load payment details"}
            </p>
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50 font-rethink">
      <div className="bg-white rounded-2xl w-full max-w-[400px] mx-4 relative p-6">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 w-8 h-8 bg-[#E8E8FF] rounded-full flex items-center justify-center hover:bg-[#D0D0FF] transition-colors"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 4L4 12M4 4L12 12"
              stroke="#6B7AFF"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>

        {/* Modal Content */}
        <div className="pt-4">
          <h2 className="text-2xl font-bold text-black mb-2">
            Payment Preview
          </h2>
          <p className="text-gray-500 mb-8 italic">
            View your payment summary below
          </p>

          {/* Payment Summary */}
          <div className="space-y-6 mb-8">
            {/* Payment Amount */}
            <div>
              <p className="text-gray-500 text-sm mb-1">Payment Amount</p>
              <p className="text-xl font-medium text-black">
                ₦{parseFloat(feeData.amount).toLocaleString()}
              </p>
            </div>

            {/* Fees */}
            <div>
              <p className="text-gray-500 text-sm mb-1">Fees</p>
              <p className="text-xl font-medium text-black">
                ₦{parseFloat(feeData.fee).toLocaleString()}
              </p>
            </div>

            {/* Total Amount - Highlighted Box */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 bg-gray-50">
              <p className="text-gray-500 text-sm mb-1">Total Amount</p>
              <p className="text-2xl font-bold text-black">
                ₦{parseFloat(calculateTotal()).toLocaleString()}
              </p>
            </div>
          </div>

          {/* Continue Button */}
          <button
            onClick={onContinue}
            className="w-full h-12 bg-[#6B7AFF] text-white rounded-full font-semibold text-base hover:bg-[#5A6AEF] transition-all"
          >
            Continue
          </button>
        </div>
      </div>
    </div>
  );
};
