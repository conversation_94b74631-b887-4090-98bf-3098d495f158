import {
  ArrowCircleLeft2,
  ArrowCircleRight2,
  Co<PERSON>,
  Location,
} from "iconsax-react";
import { Button } from "../../../../components/button/onboardingButton";
import truck from "../../../../assets/images/truck-delivery.png";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  EventDetailsResponse,
  GuestGiftsAPI,
  ItemGift,
} from "../../../../lib/apis/guestGiftsApi";
import { toast } from "react-toastify";

interface DeliveryAddressProps {
  onClose: () => void;
}

export const DeliveryAddress: React.FC<DeliveryAddressProps> = ({
  onClose,
}) => {
  const navigate = useNavigate();
  const { eventId, giftId } = useParams();
  const [eventDetails, setEventDetails] = useState<EventDetailsResponse | null>(
    null
  );
  const [itemGift, setItemGift] = useState<ItemGift | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchData();
  }, [eventId, giftId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      if (!eventId || !giftId) return;

      // Fetch event details for delivery address
      const eventResponse = await GuestGiftsAPI.getEventDetails(eventId);
      setEventDetails(eventResponse as EventDetailsResponse);

      // Fetch item gifts to get the Jumia link
      const itemGiftsResponse = await GuestGiftsAPI.getItemGifts(eventId);
      const gift = itemGiftsResponse.gifts.find((g) => g.id === giftId);
      if (gift) setItemGift(gift);
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Failed to load delivery information");
    } finally {
      setLoading(false);
    }
  };

  const handleProceed = () => {
    // Open Jumia link in new tab if available
    if (itemGift?.item_link) {
      window.open(itemGift.item_link, "_blank");
    }

    // Navigate to "Have you purchased" page
    navigate(`/guest/events/${eventId}/gifts/item/${giftId}/jumia`);
  };

  useEffect(() => {
    const scrollY = window.scrollY;
    document.body.style.position = "fixed";
    document.body.style.top = `-${scrollY}px`;
    document.body.style.width = "100%";
    document.body.style.overflow = "hidden";
    return () => {
      const scrollY = document.body.style.top;
      document.body.style.position = "";
      document.body.style.top = "";
      document.body.style.width = "";
      document.body.style.overflow = "";
      window.scrollTo(0, parseInt(scrollY || "0") * -1);
    };
  }, []);
  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
  };
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="min-h-screen px-4 pb-32 bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] flex items-center justify-center">
        <div className="pt-20">
          <Button
            variant="primary"
            size="md"
            onClick={onClose}
            className="text-primary-650 bg-white mb-5"
            iconLeft={
              <ArrowCircleLeft2 size="20" color="#4D55F2" variant="Bulk" />
            }
          >
            Back to Gift items
          </Button>
          <div className="bg-white pl-19 pr-30 pt-6 pb-9 rounded-2xl">
            <div className=" mb-11 -ml-4">
              <img
                src={truck}
                alt="delivery-truck"
                className="h-[108px] w-[105px]"
              />
            </div>
            <h2 className="text-[22px] font-medium text-grey-750 mb-1">
              Delivery Address
            </h2>
            <p className="text-sm text-grey-100 mb-12">
              Before you leave you'll need the recipient's delivery
              address.please copy the details below.
            </p>
            {loading ? (
              <div className="flex items-center justify-center mb-17">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-650"></div>
                <span className="ml-2 text-grey-100">
                  Loading delivery information...
                </span>
              </div>
            ) : eventDetails?.delivery_address ? (
              <div className="flex items-center bg-white border border-[#D5D7DA] rounded-[64px] px-4 py-2">
                <Location
                  size={20}
                  color="#292D32"
                  variant="Bulk"
                  className="mr-2"
                />
                <span className="text-[16px] font-bold text-black flex-1">
                  {eventDetails.delivery_address}
                </span>
                <button
                  className="flex items-center gap-1 bg-[#F5F9FF] text-[#5F66F3] font-semibold text-[14px] rounded-[16px] px-3 py-1 ml-2 hover:bg-[#e0e7ff] transition-colors"
                  onClick={() =>
                    handleCopy(eventDetails.delivery_address || "")
                  }
                >
                  <Copy size={16} color="#4D55F2" variant="Bulk" /> Copy
                </button>
              </div>
            ) : (
              <p className="text-base font-medium mb-17 leading-relaxed max-w-[497px] w-full">
                We currently don't have this recipient's delivery address,
                please ask them where they would like their gifts delivered
              </p>
            )}
            <Button
              variant="primary"
              size="md"
              onClick={handleProceed}
              disabled={loading}
              className={`text-white mt-5 bg-primary-650 disabled:opacity-50 disabled:cursor-not-allowed`}
              iconRight={
                <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
              }
            >
              Proceed{" "}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
