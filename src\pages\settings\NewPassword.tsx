/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef, useState } from "react";
import { Eye, EyeSlash } from "iconsax-react";
import { useMutation } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { AuthServices } from "../../lib/services/auth";

interface NewPasswordProps {
  currentPassword: string;
  onSave: () => void;
  onClose: () => void;
}

export const NewPassword = ({
  currentPassword,
  onSave,
  onClose,
}: NewPasswordProps) => {
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState("");
  const [passwordValidation, setPasswordValidation] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false,
  });
  const modalRef = useRef<HTMLDivElement>(null);

  // Password validation function
  const validatePassword = (password: string) => {
    const validation = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[!@#$%^&*()_+\-=[\]{}|;:,.<>?]/.test(password),
    };
    setPasswordValidation(validation);
    return Object.values(validation).every(Boolean);
  };

  const updatePasswordMutation = useMutation({
    mutationFn: (data: { new_password: string; old_password: string }) =>
      AuthServices.updateUserPassword(data),
    onSuccess: () => {
      toast.success("Password changed successfully");
      onSave();
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);

  useEffect(() => {
    if (newPassword) {
      const isValidPassword = validatePassword(newPassword);

      if (!isValidPassword) {
        setError("Password does not meet all requirements");
      } else if (confirmPassword && newPassword !== confirmPassword) {
        setError("Passwords do not match");
      } else {
        setError("");
      }
    } else {
      // Reset validation when password is empty
      setPasswordValidation({
        length: false,
        uppercase: false,
        lowercase: false,
        number: false,
        special: false,
      });
      setError("");
    }
  }, [newPassword, confirmPassword]);

  const handleSave = () => {
    const isValidPassword = validatePassword(newPassword);
    if (!error && isValidPassword && newPassword === confirmPassword) {
      updatePasswordMutation.mutate({
        old_password: currentPassword,
        new_password: newPassword,
      });
    }
  };

  return (
    <div
      ref={modalRef}
      className="relative mt-10 bg-white rounded-2xl font-rethink shadow-[0px_12px_120px_0px_#5F5F5F0F] w-full p-4 z-50"
    >
      <div className="mb-6">
        <h3 className="text-sm font-medium mb-4.5 tracking-[0.12em] text-primary">
          PASSWORD & SECURITY
        </h3>
        <h2 className="text-2xl font-medium mb-1.5">Change Password</h2>
        <p className="text-grey-950 text-base">
          Update your password to stay secure.
        </p>
      </div>
      <div className="flex gap-0 w-[81px] bg-gray-200 rounded-full">
        <div className="h-[8px] bg-primary-750 rounded-full w-full"></div>
      </div>

      <div className="mt-3 mb-4 relative">
        <label className="text-grey-500 text-sm font-medium mb-1.5 block">
          New Password
        </label>
        <div className="relative">
          <input
            type={showNewPassword ? "text" : "password"}
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            placeholder="Enter New Password"
            className="w-full text-grey-50 outline-none py-2.5 px-3.5 border border-gray-300 rounded-full"
          />
          <button
            type="button"
            onClick={() => setShowNewPassword(!showNewPassword)}
            className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer"
          >
            {showNewPassword ? (
              <Eye size="20" color="#B3B3B3" variant="Bulk" />
            ) : (
              <EyeSlash size="20" color="#B3B3B3" variant="Bulk" />
            )}
          </button>
        </div>

        {/* Password Requirements */}
        {newPassword && (
          <div className="mt-2 space-y-1">
            <p className="text-xs text-gray-600 mb-2">Password must contain:</p>
            <div className="grid grid-cols-1 gap-1 text-xs">
              <div
                className={`flex items-center gap-2 ${
                  passwordValidation.length ? "text-green-600" : "text-gray-500"
                }`}
              >
                <span
                  className={`w-2 h-2 rounded-full ${
                    passwordValidation.length ? "bg-green-500" : "bg-gray-300"
                  }`}
                ></span>
                At least 8 characters
              </div>
              <div
                className={`flex items-center gap-2 ${
                  passwordValidation.uppercase
                    ? "text-green-600"
                    : "text-gray-500"
                }`}
              >
                <span
                  className={`w-2 h-2 rounded-full ${
                    passwordValidation.uppercase
                      ? "bg-green-500"
                      : "bg-gray-300"
                  }`}
                ></span>
                One uppercase letter (A-Z)
              </div>
              <div
                className={`flex items-center gap-2 ${
                  passwordValidation.lowercase
                    ? "text-green-600"
                    : "text-gray-500"
                }`}
              >
                <span
                  className={`w-2 h-2 rounded-full ${
                    passwordValidation.lowercase
                      ? "bg-green-500"
                      : "bg-gray-300"
                  }`}
                ></span>
                One lowercase letter (a-z)
              </div>
              <div
                className={`flex items-center gap-2 ${
                  passwordValidation.number ? "text-green-600" : "text-gray-500"
                }`}
              >
                <span
                  className={`w-2 h-2 rounded-full ${
                    passwordValidation.number ? "bg-green-500" : "bg-gray-300"
                  }`}
                ></span>
                One number (0-9)
              </div>
              <div
                className={`flex items-center gap-2 ${
                  passwordValidation.special
                    ? "text-green-600"
                    : "text-gray-500"
                }`}
              >
                <span
                  className={`w-2 h-2 rounded-full ${
                    passwordValidation.special ? "bg-green-500" : "bg-gray-300"
                  }`}
                ></span>
                One special character (!@#$%^&*)
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="mb-5 relative">
        <label className="text-grey-500 text-sm font-medium mb-1.5 block">
          Confirm New Password
        </label>
        <div className="relative">
          <input
            type={showConfirmPassword ? "text" : "password"}
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder="Re-Enter New Password"
            className="w-full text-grey-50 outline-none py-2.5 px-3.5 border border-gray-300 rounded-full"
          />
          <button
            type="button"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer"
          >
            {showConfirmPassword ? (
              <Eye size="20" color="#B3B3B3" variant="Bulk" />
            ) : (
              <EyeSlash size="20" color="#B3B3B3" variant="Bulk" />
            )}
          </button>
        </div>
      </div>

      {error && <p className="text-red-500 text-sm mb-4">{error}</p>}

      <button
        onClick={handleSave}
        disabled={
          !!error ||
          !newPassword ||
          !confirmPassword ||
          !Object.values(passwordValidation).every(Boolean) ||
          updatePasswordMutation.isPending
        }
        className={`w-fit flex items-center gap-2 ${
          !!error ||
          !newPassword ||
          !confirmPassword ||
          !Object.values(passwordValidation).every(Boolean) ||
          updatePasswordMutation.isPending
            ? "opacity-40 cursor-not-allowed "
            : "cursor-pointer"
        } bg-primary text-white py-2 px-3.5 rounded-full text-sm font-semibold`}
      >
        {updatePasswordMutation.isPending
          ? "Changing Password..."
          : "Change Password"}
      </button>
    </div>
  );
};
