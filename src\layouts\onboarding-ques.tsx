import { useState, useEffect } from 'react';
// import { useUserAuthStore } from '../lib/store/auth';
// import logo from '../assets/icons/Ep-Logo.svg';
// import { Link } from 'react-router-dom';
import { Icon } from '../components/icons/icon';
import { Head } from '../components/reuseables/head';

interface OnboardingLayoutProps {
  activeStep: number;
  completedSteps: number[];
  onStepChange: (step: number) => void;
}

export const OnboardingLayout = ({
  activeStep,
  completedSteps,
  onStepChange,
}: OnboardingLayoutProps) => {
  // const { userData } = useUserAuthStore();
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const steps = [
    { id: 1, name: 'Event Category' },
    { id: 2, name: 'Event Details' },
    { id: 3, name: 'Add Event Images' },
    // { id: 3, name: 'Choose your Tools' },
    // { id: 4, name: 'Invite Collaborators' },
  ];

  const isStepActive = (stepId: number) => activeStep === stepId;
  const isStepCompleted = (stepId: number) => completedSteps.includes(stepId);
  const isStepClickable = (stepId: number) =>
    isStepCompleted(stepId) ||
    stepId === activeStep ||
    stepId === activeStep - 1;

  return (
    <div className="w-full font-rethink px-4 md:px-0">
      <div
        className={`fixed top-0 w-full z-50 transition-all duration-300 ${
          isScrolled
            ? 'backdrop-blur-sm bg-white/20 border-b border-white/20'
            : 'bg-transparent'
        }`}>
        <div className="px-4">
          <Head />
        </div>
      </div>

      <div className="md:fixed  xl:left-[calc(20%-40px)] left-3 top-[120px] pt-2.5">
        <div className="md:w-[170px] flex flex-wrap py-5 md:py-0 justify-center md:justify-start md:flex-col gap-3">
          {steps.map((step) => (
            <div
              key={step.id}
              className={`flex items-center md:py-2 ${
                isStepClickable(step.id)
                  ? 'cursor-pointer'
                  : 'cursor-default opacity-60'
              } text-sm ${
                isStepActive(step.id) && !isStepCompleted(step.id)
                  ? 'text-primary-50 font-bold italic'
                  : isStepCompleted(step.id)
                  ? 'text-primary-50'
                  : 'text-grey-250'
              }`}
              onClick={() => isStepClickable(step.id) && onStepChange(step.id)}>
              {isStepActive(step.id) && !isStepCompleted(step.id) && (
                <span className="bg-cus-orange-100 h-1.5 w-1.5 rounded-full mr-2"></span>
              )}
              {isStepCompleted(step.id) && (
                <span className="mr-[5px]">
                  <Icon name="marked" />
                </span>
              )}
              <span>{step.name}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
