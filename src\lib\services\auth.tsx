import { EventParkAPI } from '../event-park-api';
import axios, { AxiosResponse } from 'axios';
import { useUserAuthStore } from '../store/auth';

export const AuthServices = {
  initiateRegistration: async (data: {
    email: string;
    first_name: string;
    last_name: string;
  }) => {
    return await EventParkAPI().post(
      '/v1/auth/user/registration/initiate',
      data
    );
  },
  verifyRegistrationOTP: async (data: {
    email: string;
    otp: string | number;
  }) => {
    return await EventParkAPI().post(
      '/v1/auth/user/registration/otp/verify',
      data
    );
  },
  completeRegistration: async (data: {
    email: string;
    first_name: string;
    last_name: string;
    password: string;
  }) => {
    return await EventParkAPI().post(
      '/v1/auth/user/registration/complete',
      data
    );
  },
  login: async (data: { email: string; password: string }) => {
    return await EventParkAPI().post(
      '/v1/auth/user/tokens/access/generate',
      data
    );
  },
  passwordResetInitiate: async (data: { email: string }) => {
    return await EventParkAPI().post(
      '/v1/auth/user/password/reset/initiate',
      data
    );
  },
  passwordResetOTP: async (data: { email: string; otp: string | number }) => {
    return await EventParkAPI().post(
      '/v1/auth/user/password/reset/otp/verify',
      data
    );
  },
  completePasswordReset: async (
    data: {
      email: string;
      password: string | number;
    },
    resetToken?: string
  ) => {
    const api = EventParkAPI();
    if (resetToken) {
      api.defaults.headers.Authorization = `Bearer ${resetToken}`;
    }

    return await api.post('/v1/auth/user/password/reset/complete', data);
  },
  initiateGoogleOAuth: async (devicePublicKey: string) => {
    return await EventParkAPI().post('/v1/auth/user/oauth/initiate', {
      provider: 'google',
      device_public_key: devicePublicKey,
      app_type: 'customers',
    });
  },
  completeGoogleOAuth: async (data: {
    challenge: string;
    device_public_key: string;
    signature: string;
  }) => {
    return await EventParkAPI().post('/v1/auth/user/oauth/complete', data);
  },
  getAuthenticatedUser: async () => {
    return await EventParkAPI().get('/v1/user');
  },
  getUser: async () => {
    return await EventParkAPI().get('/v1/user');
  },
  updateAuthenticatedUser: async (data: {
    config: {
      completed_onboarding: boolean;
    };
    first_name: string;
    last_name: string;
  }) => {
    return await EventParkAPI().patch('/v1/user', data);
  },
  logout: async () => {
    try {
      // Revoke the current access token on the server
      await EventParkAPI().post('/v1/auth/user/tokens/access/revoke');
    } catch (error) {
      // Even if revoke fails, we should still clear local auth data
      console.error('Token revocation failed:', error);
    } finally {
      // Always clear auth data regardless of revoke success/failure
      useUserAuthStore.getState().clearAuthData();
    }
  },
  updateUserPassword: async (data: {
    new_password: string;
    old_password: string;
  }) => {
    return await EventParkAPI().post('/v1/user/password/change', data);
  },
  uploadFiles: async (
    files: File | File[],
    module: string = 'profile_picture',
    eventId?: string,
    giftId?: string
  ) => {
    const formData = new FormData();
    const fileArray = Array.isArray(files) ? files : [files];
    fileArray.forEach((file) => {
      formData.append('files[]', file);
    });

    const params: Record<string, string> = { module };
    if (
      eventId &&
      [
        'event_image',
        'event_banner',
        'iv_design',
        'guestlist',
        'item_gift',
      ].includes(module)
    ) {
      params.event_id = eventId;
    }

    if (giftId && module === 'item_gift') {
      params.gift_id = giftId;
    }

    return await EventParkAPI().post('/v1/user/files', formData, {
      params,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  getToolStatus: async () => {
    return await EventParkAPI().get('/v1/user/tools');
  },
  refreshToken: async (): Promise<AxiosResponse> => {
    // Get current refresh token from store
    const { refreshToken, isRefreshTokenExpired, userAppToken } =
      useUserAuthStore.getState();
    const baseURL = import.meta.env.VITE_API_BASE_URL;

    console.log('🔄 Attempting token refresh...');
    console.log('Refresh token available:', !!refreshToken);
    console.log('Refresh token expired:', isRefreshTokenExpired());
    console.log('Base URL:', baseURL);

    if (!refreshToken) {
      console.error('❌ No refresh token available');
      throw new Error('No refresh token available');
    }

    if (isRefreshTokenExpired()) {
      console.error('❌ Refresh token is expired');
      throw new Error('Refresh token is expired');
    }

    console.log('🚀 Making refresh token API call...');
    console.log(
      'Refresh token in body:',
      `${refreshToken.substring(0, 20)}...`
    );

    try {
      // Use EventParkAPI but create a direct axios call to bypass interceptors
      const baseURL = import.meta.env.VITE_API_BASE_URL;
      const response = await axios.post(
        `${baseURL}/v1/auth/user/tokens/access/refresh`,
        { refresh_token: refreshToken }, // Send refresh token in request body as per API docs
        {
          timeout: 30000,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${userAppToken}`,
          },
        }
      );
      console.log('✅ Token refresh successful');
      return response;
    } catch (error) {
      console.error('❌ Token refresh failed:', error);
      if (axios.isAxiosError(error)) {
        console.error('Response status:', error.response?.status);
        console.error('Response data:', error.response?.data);
        console.error('Request headers:', error.config?.headers);
      }
      throw error;
    }
  },
  updateUserTransactionPin: async (data: {
    new_pin: string;
    old_pin: string;
  }) => {
    return await EventParkAPI().patch('/v1/user/pins/transaction', data);
  },
  createUserTransactionPin: async (data: { pin: string }) => {
    return await EventParkAPI().post('/v1/user/pins/transaction', data);
  },
};
