import { useState } from 'react';
import ex1 from '../../../assets/images/invitation.png';
import { ArrowCircleRight2, CloseCircle } from 'iconsax-react';
import { useEventStore } from '../../../lib/store/event';
import { useGuestListManagement } from '../../../lib/hooks/useGuestListManagement';
import { useEventManagement } from '../../../lib/hooks/useEventManagement';
import { GuestList } from '../../../lib/services/guest-list';
import { events } from '../../../lib/services/events';
import { toast } from 'react-toastify';
import { useToolStatusRefresh } from '../../../lib/hooks/useToolStatusRefresh';
import { EventPreferenceModal } from '../../../components/modals';
import { completeEventDataEmitter } from '../../../lib/hooks/useCompleteEventData';

interface Template {
  id?: string;
  name: string;
  preview_url?: string;
  image?: string;
}

interface Reminder {
  id: number;
  text: string;
  checked: boolean;
}

interface Guest {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface PreviewProps {
  onSuccess: (previewUrl?: string) => void;
  selectedTemplate: Template | null;
  guests: Guest[];
  eventId?: string;
  onGuestsChange?: (guests: Guest[]) => void;
  guestSource?: 'manual' | 'upload' | 'email' | 'link';
}

export const Preview = ({
  onSuccess,
  selectedTemplate,
  guests,
  onGuestsChange,
  guestSource = 'manual',
}: PreviewProps) => {
  const { selectedEvent } = useEventStore();
  const { createGuestList, transformGuestData, isCreatingGuestList } =
    useGuestListManagement();
  const { refreshEvents, updateEventOptimistically } = useEventManagement();
  const { refreshToolStatus } = useToolStatusRefresh();
  const [isActivating, setIsActivating] = useState(false);
  const [showPreferenceModal, setShowPreferenceModal] = useState(false);
  const [isSettingPreference, setIsSettingPreference] = useState(false);
  const [reminders, setReminders] = useState([
    { id: 1, text: 'A week before the event day', checked: true },
    { id: 2, text: '72 Hours before event day', checked: true },
    { id: 3, text: '24 Hours before event day', checked: true },
    { id: 4, text: 'On the event day', checked: false },
  ]);

  const templateImageUrl =
    selectedEvent?.iv_preview_url ||
    selectedTemplate?.preview_url ||
    selectedTemplate?.image ||
    ex1;

  const toggleCheck = (id: number): void => {
    setReminders(
      reminders.map((reminder: Reminder) =>
        reminder.id === id
          ? { ...reminder, checked: !reminder.checked }
          : reminder
      )
    );
  };

  const handleDeleteGuest = (guestId: number) => {
    if (onGuestsChange) {
      const updatedGuests = guests.filter((guest) => guest.id !== guestId);
      onGuestsChange(updatedGuests);
    }
  };
  const getInviteType = (source: string): string => {
    switch (source) {
      case 'upload':
        return 'spreadsheet';
      case 'manual':
        return 'manual';
      case 'email':
        return 'email';
      case 'link':
        return 'link';
      default:
        return 'manual';
    }
  };

  console.log(transformGuestData(guests));

  const handleCreateGuestList = () => {
    setShowPreferenceModal(true);
  };

  const handleSetPreference = async (preference: 'public' | 'private') => {
    if (!selectedEvent?.id) return;

    setIsSettingPreference(true);
    try {
      const visibility = preference === 'public' ? 'public' : 'private';
      await events.updateEventDetails(selectedEvent.id, { visibility });

      setShowPreferenceModal(false);

      const transformedGuests = transformGuestData(guests);
      const inviteType = getInviteType(guestSource);
      await createGuestList(selectedEvent.id, transformedGuests, inviteType);

      setIsActivating(true);
      const activationResponse = await GuestList.activateGuestlist(
        selectedEvent.id
      );

      const updatedEventResponse = await events.getEventByID(selectedEvent.id);
      const completeEventData = updatedEventResponse.data;

      const updatedEvent = {
        id: completeEventData.id,
        title: completeEventData.title,
        category_id: completeEventData.category_id,
        category_name: completeEventData.category_name,
        date_from: completeEventData.date_from,
        date_to: completeEventData.date_to,
        description: completeEventData.description,
        location_address: completeEventData.location_address,
        gift_registry_title: completeEventData.gift_registry_title || '',
        delivery_address: completeEventData.delivery_address || '',
        banner_image_id: completeEventData.banner_image_id,
        banner_preview_url: completeEventData.banner_preview_url,
        visibility: completeEventData.visibility,
        invite_link: completeEventData.invite_link,
        iv_preview_url: completeEventData.iv_preview_url,
        guest_count: completeEventData.guest_count || 0,
        status: completeEventData.event_status,
        user_id: completeEventData.user_id,
        created_at: completeEventData.created_at,
        updated_at: completeEventData.updated_at,
      };
      updateEventOptimistically(updatedEvent);

      await refreshEvents();
      completeEventDataEmitter.emit();
      refreshToolStatus();

      const previewUrl = activationResponse?.data?.iv_preview_url;
      onSuccess(previewUrl);
    } catch (error) {
      console.error('❌ Failed to set preference or create guest list:', error);
      toast.error(
        'Failed to set preference or create guest list. Please try again.'
      );
    } finally {
      setIsSettingPreference(false);
      setIsActivating(false);
    }
  };

  const handleCloseModal = () => {
    if (!isSettingPreference) {
      setShowPreferenceModal(false);
    }
  };

  return (
    <div className="max-w-[645px] mx-auto pt-5 md:pt-12 px-4 md:px-0 font-rethink  pb-52">
      <div className="md:ml-9 w-full  ">
        <div className="flex mb-6 justify-between items-end">
          <h2 className="md:text-[40px] text-base font-medium leading-[114.99999999999999%]">
            Preview your guest list.
            <br />
            You are good to go!
          </h2>
          <button
            type="button"
            onClick={handleCreateGuestList}
            disabled={isCreatingGuestList || isActivating}
            className={`cursor-pointer text-white flex items-center gap-2 px-3 py-2 rounded-full text-xs md:text-base font-semibold ${
              isCreatingGuestList || isActivating
                ? 'bg-primary-650/50 cursor-not-allowed'
                : 'bg-primary-650 hover:bg-primary/50'
            }`}>
            <span>
              {isCreatingGuestList
                ? 'Creating...'
                : isActivating
                ? 'Activating...'
                : 'Send Invite'}
            </span>
            {isCreatingGuestList || isActivating ? (
              <div className="animate-spin h-6 w-6 border-2 border-white border-t-transparent rounded-full" />
            ) : (
              <ArrowCircleRight2 color="#FFFFFF" size="24" variant="Bulk" />
            )}
          </button>
        </div>
        <div className="bg-white rounded-[20px] md:flex justify-between">
          <div className="md:max-w-[265px] w-full border-r border-grey-850">
            <img
              src={templateImageUrl}
              alt={selectedTemplate?.name || 'Invitation Template'}
              className="w-full h-auto object-contain rounded-t-2xl"
              style={{
                aspectRatio: '265/300',
                maxHeight: '280px',
              }}
            />
            <div className="px-5 mt-4 mb-10">
              <h1 className="text-sm mb-4 font-bold text-grey-700 tracking-[0.16em]">
                SEND REMINDERS
              </h1>

              <div className="space-y-8">
                {reminders.map((reminder) => (
                  <div key={reminder.id} className="flex items-center">
                    <div
                      className={`w-5.5 h-5.5 rounded-full flex items-center justify-center mr-2 cursor-pointer
                  ${
                    reminder.checked
                      ? 'bg-primary-650'
                      : 'bg-white border border-primary-650'
                  }`}
                      onClick={() => toggleCheck(reminder.id)}>
                      {reminder.checked && (
                        <svg
                          width={24}
                          height={24}
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M9 12L11 14L15 10"
                            stroke="white"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      )}
                    </div>
                    <span
                      className={`text-sm  font-semibold ${
                        reminder.checked
                          ? 'text-dark-blue-400 italic'
                          : 'text-grey-250'
                      }`}>
                      {reminder.text}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className="flex-1 mt-10 md:mt-0">
            <h2 className="py-[19px] pl-4 text-lg font-medium border-b border-grey-150 shadow-[0px_12px_120px_0px_#5F5F5F0F]">
              Guests
            </h2>
            <div className="px-6 pt-5">
              {guests.map((guest, index) => (
                <div
                  key={index}
                  className="w-full rounded-2xl mb-3 bg-white border border-grey-150 py-4  pl-4 pr-2.5 flex justify-between items-start">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center ">
                      <div className="w-10 h-10 bg-gradient-to-br from-[#FEF7F4] from-26.3% to-[#F5F6FE] to-75.01% rounded-full flex items-center justify-center text-dark-blue-200 font-semibold text-base">
                        {(guest?.firstName?.charAt(0)?.toUpperCase() || '') +
                          (guest?.lastName?.charAt(0)?.toUpperCase() || '')}
                      </div>
                    </div>
                    <div>
                      <h3 className="text-dark-blue-400 font-semibold text-sm">
                        {guest.firstName + ' ' + guest.lastName}
                      </h3>
                      <p className="text-grey-650 text-xs">
                        {guest.email} • {guest.phone}
                      </p>
                    </div>
                  </div>
                  <button
                    className="cursor-pointer"
                    onClick={() => handleDeleteGuest(guest.id)}>
                    <CloseCircle size="20" color="#000059" variant="Bulk" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      <EventPreferenceModal
        isOpen={showPreferenceModal}
        onClose={handleCloseModal}
        onSetPreference={handleSetPreference}
        isLoading={isSettingPreference}
      />
    </div>
  );
};
